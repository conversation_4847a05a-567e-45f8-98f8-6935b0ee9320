"""
Configuration settings for the magnet converter module
磁力链接转换模块的配置设置
"""

import os
from pathlib import Path

# Base configuration
BASE_DIR = Path(__file__).parent.parent
CAPTURES_DIR = BASE_DIR / "captures"

# Target website configuration
TARGET_WEBSITE = {
    "url": "https://xiongmaobt.org/",
    "name": "xiongmaobt",
    "timeout": 30,
    "retry_attempts": 3
}

# Browser configuration
BROWSER_CONFIG = {
    "headless": False,
    "window_size": (1920, 1080),
    "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
    "disable_images": False,
    "disable_javascript": False,
    "page_load_timeout": 30
}

# Capture configuration
CAPTURE_CONFIG = {
    "auto_screenshot": True,
    "auto_html_save": True,
    "auto_network_log": True,
    "monitor_interval": 2,  # seconds
    "max_html_size": 10 * 1024 * 1024,  # 10MB
    "screenshot_format": "png",
    "screenshot_quality": 90
}

# Logging configuration
LOGGING_CONFIG = {
    "level": "INFO",
    "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    "file_rotation": "1 day",
    "max_log_files": 7
}

# Anti-detection configuration
ANTI_DETECTION = {
    "random_delays": True,
    "min_delay": 1,
    "max_delay": 3,
    "rotate_user_agents": False,
    "use_proxy": False,
    "proxy_list": []
}

# Environment-based overrides
def get_config_value(key: str, default=None):
    """Get configuration value from environment or default"""
    return os.getenv(f"MAGNET_CONVERTER_{key.upper()}", default)

# Override with environment variables if available
if get_config_value("headless"):
    BROWSER_CONFIG["headless"] = get_config_value("headless").lower() == "true"

if get_config_value("target_url"):
    TARGET_WEBSITE["url"] = get_config_value("target_url")

if get_config_value("captures_dir"):
    CAPTURES_DIR = Path(get_config_value("captures_dir"))

# Ensure directories exist
CAPTURES_DIR.mkdir(exist_ok=True)
(CAPTURES_DIR / "html").mkdir(exist_ok=True)
(CAPTURES_DIR / "screenshots").mkdir(exist_ok=True)
(CAPTURES_DIR / "network").mkdir(exist_ok=True)
(CAPTURES_DIR / "interactions").mkdir(exist_ok=True)
(CAPTURES_DIR / "logs").mkdir(exist_ok=True)
