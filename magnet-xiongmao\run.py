#!/usr/bin/env python3
"""
Simple runner for XiongmaoBT scraper
XiongmaoBT爬虫的简单运行器
"""

import sys
from pathlib import Path

# Add current directory to path for direct execution
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

try:
    from scraper import XiongmaoBTScraper
    from config import Config
    from examples import run_examples
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("Please ensure you're in the correct directory and DrissionPage is installed:")
    print("pip install DrissionPage")
    sys.exit(1)


def quick_start():
    """Quick start with default settings"""
    print("🚀 XiongmaoBT Scraper - Quick Start")
    print("=" * 40)
    print("Starting with default settings...")
    print("- Interactive mode")
    print("- GUI browser window")
    print("- Default capture directory")
    print("=" * 40)
    
    try:
        with XiongmaoBTScraper() as scraper:
            if scraper.navigate_to_target():
                print("✅ Ready for interaction!")
                scraper.start_interactive_session()
            else:
                print("❌ Failed to navigate to website")
                return 1
        return 0
    except Exception as e:
        print(f"❌ Error: {e}")
        return 1


def main():
    """Main menu"""
    print("🎯 XiongmaoBT Scraper")
    print("=" * 30)
    print("1. Quick Start (Default Settings)")
    print("2. Run Examples")
    print("3. Advanced Mode (with options)")
    print("0. Exit")
    print("=" * 30)
    
    while True:
        try:
            choice = input("\nSelect option (0-3): ").strip()
            
            if choice == "0":
                print("👋 Goodbye!")
                break
            elif choice == "1":
                return quick_start()
            elif choice == "2":
                run_examples()
            elif choice == "3":
                print("💡 For advanced options, use:")
                print("python -m magnet_xiongmao.main --help")
                print("\nOr import the module in your code:")
                print("from magnet_xiongmao import XiongmaoBTScraper, Config")
            else:
                print("❌ Invalid choice. Please select 0-3.")
                
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"❌ Error: {e}")
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
