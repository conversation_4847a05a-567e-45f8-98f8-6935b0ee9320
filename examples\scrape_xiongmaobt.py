"""
Example script for scraping xiongmaobt.org
使用示例：抓取xiongmaobt.org网站
"""

import sys
import os
from pathlib import Path

# Add parent directory to path to import magnet_converter
sys.path.append(str(Path(__file__).parent.parent))

from magnet_converter import WebScraper


def main():
    """
    Main example function
    演示如何使用WebScraper进行网站交互捕获
    """
    print("=== Xiongmaobt.org Web Scraper Example ===")
    print("This example will:")
    print("1. Open a browser window")
    print("2. Navigate to xiongmaobt.org")
    print("3. Wait for your interaction")
    print("4. Capture all changes and interactions")
    print("5. Save detailed logs and screenshots")
    print()
    
    # Create captures directory
    capture_dir = "captures_xiongmaobt"
    
    try:
        # Initialize scraper (headless=False to see the browser)
        scraper = WebScraper(headless=False, capture_dir=capture_dir)
        
        print("Setting up browser...")
        scraper.setup_browser()
        
        print("Navigating to xiongmaobt.org...")
        scraper.navigate_to_target()
        
        print("\n" + "="*50)
        print("BROWSER IS READY FOR INTERACTION")
        print("="*50)
        print("Instructions:")
        print("1. The browser window is now open and ready")
        print("2. You can interact with the website normally")
        print("3. All your actions will be automatically captured")
        print("4. Press Enter in this terminal to manually capture current state")
        print("5. Type 'q' and press Enter to quit")
        print("6. Check the 'captures_xiongmaobt' folder for all captured data")
        print()
        
        # Wait for user interaction
        scraper.wait_for_interaction()
        
        print("\nClosing browser and saving final captures...")
        scraper.close()
        
        print(f"\n✅ All captures saved to: {capture_dir}")
        print("📁 Check the following folders:")
        print(f"   - {capture_dir}/html/ - HTML snapshots")
        print(f"   - {capture_dir}/screenshots/ - Screenshots")
        print(f"   - {capture_dir}/interactions/ - Detailed interaction logs")
        print(f"   - {capture_dir}/interaction_log_*.json - Complete session log")
        
    except KeyboardInterrupt:
        print("\n⚠️  Scraper interrupted by user")
    except Exception as e:
        print(f"❌ Error occurred: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n🎯 Next steps:")
    print("1. Review the captured data to understand the website structure")
    print("2. Identify patterns for captcha handling")
    print("3. Develop automation strategies based on the captured interactions")


if __name__ == "__main__":
    main()
