"""
Configuration for Xiongmaobt.org scraper
xiongmaobt.org爬虫的配置文件
"""

import os
from pathlib import Path
from typing import Dict, Any, Optional


class Config:
    """Configuration class for XiongmaoBT scraper"""
    
    # Base paths
    BASE_DIR = Path(__file__).parent.parent
    DEFAULT_CAPTURE_DIR = BASE_DIR / "captures" / "xiongmaobt"
    
    # Target website
    TARGET_URL = "https://xiongmaobt.org/"
    SITE_NAME = "xiongmaobt"
    
    # Browser settings
    BROWSER_CONFIG = {
        "headless": False,
        "window_size": (1920, 1080),
        "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
        "page_load_timeout": 30,
        "implicit_wait": 10
    }
    
    # Anti-detection settings
    ANTI_DETECTION = {
        "disable_blink_features": True,
        "exclude_switches": ["enable-automation"],
        "use_automation_extension": False,
        "remove_webdriver_property": True,
        "random_delays": True,
        "min_delay": 1.0,
        "max_delay": 3.0
    }
    
    # Capture settings
    CAPTURE_CONFIG = {
        "auto_screenshot": True,
        "auto_html_save": True,
        "screenshot_format": "png",
        "screenshot_quality": 90,
        "max_html_size": 10 * 1024 * 1024,  # 10MB
        "save_cookies": True,
        "save_local_storage": True,
        "save_session_storage": True
    }
    
    # Monitoring settings
    MONITOR_CONFIG = {
        "default_interval": 2,  # seconds
        "detect_url_changes": True,
        "detect_content_changes": True,
        "detect_element_changes": True,
        "max_monitoring_time": 3600  # 1 hour
    }
    
    # Logging settings
    LOGGING_CONFIG = {
        "level": "INFO",
        "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        "save_to_file": True,
        "max_log_size": "10MB",
        "backup_count": 5
    }
    
    def __init__(self, capture_dir: Optional[str] = None, **kwargs):
        """
        Initialize configuration
        
        Args:
            capture_dir: Custom capture directory
            **kwargs: Override any config values
        """
        self.capture_dir = Path(capture_dir) if capture_dir else self.DEFAULT_CAPTURE_DIR
        
        # Apply any overrides
        for key, value in kwargs.items():
            if hasattr(self, key.upper()):
                setattr(self, key.upper(), value)
        
        # Ensure capture directories exist
        self._create_directories()
        
        # Apply environment variable overrides
        self._apply_env_overrides()
    
    def _create_directories(self):
        """Create necessary directories"""
        directories = [
            self.capture_dir,
            self.capture_dir / "html",
            self.capture_dir / "screenshots", 
            self.capture_dir / "interactions",
            self.capture_dir / "logs",
            self.capture_dir / "network"
        ]
        
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)
    
    def _apply_env_overrides(self):
        """Apply environment variable overrides"""
        env_mappings = {
            "XIONGMAO_HEADLESS": ("BROWSER_CONFIG", "headless", bool),
            "XIONGMAO_TARGET_URL": ("TARGET_URL", None, str),
            "XIONGMAO_CAPTURE_DIR": ("capture_dir", None, Path),
            "XIONGMAO_USER_AGENT": ("BROWSER_CONFIG", "user_agent", str),
            "XIONGMAO_MONITOR_INTERVAL": ("MONITOR_CONFIG", "default_interval", int)
        }
        
        for env_var, (config_attr, sub_key, type_func) in env_mappings.items():
            env_value = os.getenv(env_var)
            if env_value:
                try:
                    if type_func == bool:
                        value = env_value.lower() in ('true', '1', 'yes', 'on')
                    else:
                        value = type_func(env_value)
                    
                    if sub_key:
                        getattr(self, config_attr)[sub_key] = value
                    else:
                        setattr(self, config_attr, value)
                except (ValueError, TypeError) as e:
                    print(f"Warning: Invalid value for {env_var}: {env_value} ({e})")
    
    def get_browser_options(self) -> Dict[str, Any]:
        """Get browser configuration options"""
        return self.BROWSER_CONFIG.copy()
    
    def get_anti_detection_settings(self) -> Dict[str, Any]:
        """Get anti-detection settings"""
        return self.ANTI_DETECTION.copy()
    
    def get_capture_settings(self) -> Dict[str, Any]:
        """Get capture configuration"""
        return self.CAPTURE_CONFIG.copy()
    
    def get_monitor_settings(self) -> Dict[str, Any]:
        """Get monitoring configuration"""
        return self.MONITOR_CONFIG.copy()
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert configuration to dictionary"""
        return {
            "target_url": self.TARGET_URL,
            "site_name": self.SITE_NAME,
            "capture_dir": str(self.capture_dir),
            "browser_config": self.BROWSER_CONFIG,
            "anti_detection": self.ANTI_DETECTION,
            "capture_config": self.CAPTURE_CONFIG,
            "monitor_config": self.MONITOR_CONFIG,
            "logging_config": self.LOGGING_CONFIG
        }
