#!/usr/bin/env python3
"""
Test script to verify all imports work correctly
测试脚本，验证所有导入是否正常工作
"""

def test_imports():
    """Test all module imports"""
    print("🧪 Testing imports...")
    
    try:
        print("  Testing DrissionPage...")
        import DrissionPage
        print("  ✅ DrissionPage imported successfully")
    except ImportError as e:
        print(f"  ❌ DrissionPage import failed: {e}")
        print("  Please install: pip install DrissionPage")
        return False
    
    try:
        print("  Testing config...")
        from config import Config
        print("  ✅ Config imported successfully")
    except ImportError as e:
        print(f"  ❌ Config import failed: {e}")
        return False
    
    try:
        print("  Testing utils...")
        from utils import CaptureManager, SessionLogger, PageMonitor, InteractionHelper
        print("  ✅ Utils imported successfully")
    except ImportError as e:
        print(f"  ❌ Utils import failed: {e}")
        return False
    
    try:
        print("  Testing scraper...")
        from scraper import XiongmaoBTScraper
        print("  ✅ Scraper imported successfully")
    except ImportError as e:
        print(f"  ❌ Scraper import failed: {e}")
        return False
    
    try:
        print("  Testing examples...")
        from examples import run_examples
        print("  ✅ Examples imported successfully")
    except ImportError as e:
        print(f"  ❌ Examples import failed: {e}")
        return False
    
    print("\n🎉 All imports successful!")
    return True


def test_basic_functionality():
    """Test basic functionality"""
    print("\n🔧 Testing basic functionality...")
    
    try:
        from config import Config
        from scraper import XiongmaoBTScraper
        
        # Test config creation
        config = Config(headless=True, capture_dir="test_captures")
        print("  ✅ Config creation successful")
        
        # Test scraper creation (don't initialize browser)
        scraper = XiongmaoBTScraper(config=config)
        print("  ✅ Scraper creation successful")
        
        # Test config access
        info = config.to_dict()
        print(f"  ✅ Config info: {len(info)} settings loaded")
        
        print("\n🎉 Basic functionality test passed!")
        return True
        
    except Exception as e:
        print(f"  ❌ Basic functionality test failed: {e}")
        return False


def main():
    """Main test function"""
    print("🚀 XiongmaoBT Module Test")
    print("=" * 40)
    
    # Test imports
    if not test_imports():
        print("\n❌ Import tests failed!")
        return 1
    
    # Test basic functionality
    if not test_basic_functionality():
        print("\n❌ Functionality tests failed!")
        return 1
    
    print("\n" + "="*40)
    print("✅ All tests passed!")
    print("\n🎯 You can now run:")
    print("  python run.py        # Simple runner")
    print("  python main.py       # Main program")
    print("  python launcher.py   # Launcher menu")
    print("  python examples.py   # Examples")
    
    return 0


if __name__ == "__main__":
    import sys
    sys.exit(main())
