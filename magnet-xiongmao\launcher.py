#!/usr/bin/env python3
"""
XiongmaoBT Scraper Launcher
XiongmaoBT爬虫启动器
"""

import sys
import subprocess
from pathlib import Path

def main():
    """Main launcher"""
    print("🚀 XiongmaoBT Scraper Launcher")
    print("=" * 40)
    
    # Check if DrissionPage is installed
    try:
        import DrissionPage
        print("✅ DrissionPage is installed")
    except ImportError:
        print("❌ DrissionPage not found!")
        print("Please install it with: pip install DrissionPage")
        return 1
    
    current_dir = Path(__file__).parent
    print("📁 Module location:", current_dir)
    print("=" * 40)
    print()
    
    # Show options
    print("Select launch method:")
    print("1. Simple runner (interactive menu)")
    print("2. Main program (with command line options)")
    print("3. Examples")
    print("4. Help")
    print("0. Exit")
    print()
    
    while True:
        try:
            choice = input("Enter choice (0-4): ").strip()
            
            if choice == "0":
                print("👋 Goodbye!")
                break
            elif choice == "1":
                # Run simple runner
                print("🚀 Starting simple runner...")
                subprocess.run([sys.executable, "run.py"], cwd=current_dir)
                break
            elif choice == "2":
                # Run main program
                print("🚀 Starting main program...")
                print("💡 You can also run: python main.py --help")
                subprocess.run([sys.executable, "main.py"], cwd=current_dir)
                break
            elif choice == "3":
                # Run examples
                print("🚀 Starting examples...")
                subprocess.run([sys.executable, "examples.py"], cwd=current_dir)
                break
            elif choice == "4":
                # Show help
                print("\n📖 Help:")
                print("Available commands from magnet-xiongmao directory:")
                print("  python launcher.py                        # This launcher")
                print("  python run.py                             # Simple runner")
                print("  python main.py                            # Main program")
                print("  python examples.py                        # Examples")
                print("  python -m magnet_xiongmao.main            # As module")
                print()
                print("Or import in your code:")
                print("  from magnet_xiongmao import XiongmaoBTScraper, quick_start")
                print()
            else:
                print("❌ Invalid choice. Please enter 0-4.")
                
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"❌ Error: {e}")
            break
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
