"""
Magnet-Xiongmao Module
专门用于xiongmaobt.org网站的磁力链接获取模块
"""

from .scraper import XiongmaoBTScraper
from .config import Config
from .utils import CaptureManager, SessionLogger, PageMonitor, InteractionHelper

__version__ = "1.0.0"
__author__ = "PikPak Video Library"
__description__ = "Automated scraper for xiongmaobt.org magnet links"

__all__ = [
    "XiongmaoBTScraper",
    "Config",
    "CaptureManager",
    "SessionLogger",
    "PageMonitor",
    "InteractionHelper"
]

# Convenience function for quick start
def quick_start(**kwargs):
    """
    Quick start function for immediate use

    Args:
        **kwargs: Configuration options passed to Config

    Returns:
        XiongmaoBTScraper: Configured scraper instance
    """
    config = Config(**kwargs)
    return XiongmaoBTScraper(config=config)
