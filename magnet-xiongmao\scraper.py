"""
XiongmaoBT Scraper - Core scraping functionality
xiongmaobt.org网站的核心爬虫功能
"""

import time
from datetime import datetime
from typing import Optional, Dict, Any
from pathlib import Path

try:
    from DrissionPage import ChromiumPage, ChromiumOptions
    from DrissionPage.errors import ElementNotFoundError, PageDisconnectedError
except ImportError:
    raise ImportError("DrissionPage is required. Install with: pip install DrissionPage")

from .config import Config
from .utils import CaptureManager, SessionLogger, PageMonitor, InteractionHelper


class XiongmaoBTScraper:
    """
    Main scraper class for xiongmaobt.org
    专门用于xiongmaobt.org的主要爬虫类
    """
    
    def __init__(self, config: Optional[Config] = None, **kwargs):
        """
        Initialize the scraper
        
        Args:
            config: Configuration object
            **kwargs: Override config parameters
        """
        self.config = config or Config(**kwargs)
        self.page: Optional[ChromiumPage] = None
        self.session_id = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Initialize utilities
        log_file = self.config.capture_dir / f"session_log_{self.session_id}.json"
        self.logger = SessionLogger(self.session_id, log_file)
        self.capture_manager = CaptureManager(self.config.capture_dir, self.session_id)
        self.monitor: Optional[PageMonitor] = None
        
        # State tracking
        self.is_initialized = False
        self.start_time = datetime.now()
        
        print(f"🚀 XiongmaoBT Scraper initialized")
        print(f"📁 Session ID: {self.session_id}")
        print(f"💾 Capture directory: {self.config.capture_dir}")
    
    def setup_browser(self) -> None:
        """Setup and configure the browser"""
        if self.is_initialized:
            print("⚠️  Browser already initialized")
            return
        
        try:
            print("🔧 Setting up browser...")
            
            # Configure browser options
            options = ChromiumOptions()
            browser_config = self.config.get_browser_options()
            anti_detection = self.config.get_anti_detection_settings()
            
            # Apply browser settings
            if browser_config.get("headless"):
                options.headless()
            
            # Window size
            window_size = browser_config.get("window_size", (1920, 1080))
            options.add_argument(f'--window-size={window_size[0]},{window_size[1]}')
            
            # Anti-detection settings
            if anti_detection.get("disable_blink_features"):
                options.add_argument('--disable-blink-features=AutomationControlled')
            
            if anti_detection.get("exclude_switches"):
                options.add_experimental_option("excludeSwitches", ["enable-automation"])
            
            if not anti_detection.get("use_automation_extension", True):
                options.add_experimental_option('useAutomationExtension', False)
            
            # Additional options for stability
            options.add_argument('--no-sandbox')
            options.add_argument('--disable-dev-shm-usage')
            options.add_argument('--disable-gpu')
            options.add_argument('--disable-extensions')
            
            # Set user agent
            user_agent = browser_config.get("user_agent")
            if user_agent:
                options.add_argument(f'--user-agent={user_agent}')
            
            # Initialize page
            self.page = ChromiumPage(addr_or_opts=options)
            
            # Remove webdriver property if configured
            if anti_detection.get("remove_webdriver_property"):
                self.page.run_js("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            
            # Set timeouts
            page_timeout = browser_config.get("page_load_timeout", 30)
            implicit_wait = browser_config.get("implicit_wait", 10)
            
            self.page.set.timeouts(page_timeout, implicit_wait)
            
            # Initialize monitor
            self.monitor = PageMonitor(self.page, self.capture_manager, self.logger)
            
            self.is_initialized = True
            
            # Log successful setup
            self.logger.log_interaction("browser_setup", {
                "status": "success",
                "config": browser_config,
                "anti_detection": anti_detection,
                "user_agent": self.page.run_js("return navigator.userAgent"),
                "viewport": self.page.run_js("return {width: window.innerWidth, height: window.innerHeight}")
            })
            
            print("✅ Browser setup completed")
            
        except Exception as e:
            self.logger.log_interaction("browser_setup", {
                "status": "error",
                "error": str(e)
            })
            print(f"❌ Browser setup failed: {e}")
            raise
    
    def navigate_to_target(self) -> bool:
        """
        Navigate to the target website
        
        Returns:
            bool: True if navigation successful, False otherwise
        """
        if not self.is_initialized:
            raise RuntimeError("Browser not initialized. Call setup_browser() first.")
        
        try:
            print(f"🌐 Navigating to {self.config.TARGET_URL}...")
            
            # Navigate to the website
            self.page.get(self.config.TARGET_URL)
            
            # Wait for page to load
            time.sleep(3)
            
            # Capture initial state
            captured_files = self.capture_manager.capture_page_state(self.page, "initial_load")
            
            # Log navigation
            self.logger.log_interaction("navigation", {
                "status": "success",
                "target_url": self.config.TARGET_URL,
                "final_url": self.page.url,
                "title": self.page.title,
                "captured_files": captured_files
            })
            
            print(f"✅ Successfully loaded: {self.page.title}")
            print(f"🔗 Current URL: {self.page.url}")
            
            return True
            
        except Exception as e:
            self.logger.log_interaction("navigation", {
                "status": "error",
                "target_url": self.config.TARGET_URL,
                "error": str(e)
            })
            print(f"❌ Navigation failed: {e}")
            return False
    
    def start_interactive_session(self) -> None:
        """Start interactive session for manual interaction"""
        if not self.is_initialized:
            raise RuntimeError("Browser not initialized. Call setup_browser() first.")
        
        print("\n" + "🎯 Starting interactive session...")
        print("You can now interact with the website normally.")
        print("All interactions will be automatically captured.")
        
        # Start user interaction
        InteractionHelper.wait_for_user_input(
            self.capture_manager, 
            self.page, 
            self.logger
        )
    
    def start_monitoring(self, interval: float = None, max_duration: int = None) -> None:
        """
        Start automatic page monitoring
        
        Args:
            interval: Monitoring interval in seconds
            max_duration: Maximum monitoring duration in seconds
        """
        if not self.monitor:
            raise RuntimeError("Monitor not initialized. Call setup_browser() first.")
        
        monitor_config = self.config.get_monitor_settings()
        interval = interval or monitor_config.get("default_interval", 2)
        max_duration = max_duration or monitor_config.get("max_monitoring_time", 3600)
        
        self.monitor.start_monitoring(interval, max_duration)
    
    def capture_current_state(self, action_name: str = None) -> Dict[str, str]:
        """
        Manually capture current page state
        
        Args:
            action_name: Name for this capture
            
        Returns:
            Dictionary with captured file paths
        """
        if not self.is_initialized:
            raise RuntimeError("Browser not initialized.")
        
        action_name = action_name or f"manual_{datetime.now().strftime('%H%M%S')}"
        return self.capture_manager.capture_page_state(self.page, action_name)
    
    def get_page_info(self) -> Dict[str, Any]:
        """Get current page information"""
        if not self.page:
            return {"error": "Browser not initialized"}
        
        try:
            return {
                "url": self.page.url,
                "title": self.page.title,
                "ready_state": self.page.run_js("return document.readyState"),
                "viewport": self.page.run_js("return {width: window.innerWidth, height: window.innerHeight}"),
                "scroll_position": self.page.run_js("return {x: window.pageXOffset, y: window.pageYOffset}"),
                "cookies_count": len(self.page.cookies()),
                "session_id": self.session_id,
                "capture_count": self.capture_manager.get_capture_count(),
                "interaction_count": self.logger.get_interaction_count()
            }
        except Exception as e:
            return {"error": f"Failed to get page info: {e}"}
    
    def close(self) -> None:
        """Close the browser and save final data"""
        if self.page:
            try:
                # Capture final state
                self.capture_manager.capture_page_state(self.page, "session_end")
                
                # Log session end
                session_duration = (datetime.now() - self.start_time).total_seconds()
                self.logger.log_interaction("session_end", {
                    "session_duration": session_duration,
                    "total_captures": self.capture_manager.get_capture_count(),
                    "total_interactions": self.logger.get_interaction_count(),
                    "final_url": self.page.url,
                    "final_title": self.page.title
                })
                
                # Close browser
                self.page.quit()
                
                print(f"\n✅ Session ended successfully")
                print(f"⏱️  Duration: {session_duration:.1f} seconds")
                print(f"📸 Total captures: {self.capture_manager.get_capture_count()}")
                print(f"📝 Total interactions: {self.logger.get_interaction_count()}")
                print(f"💾 Data saved to: {self.config.capture_dir}")
                
            except Exception as e:
                print(f"⚠️  Error during cleanup: {e}")
            finally:
                self.page = None
                self.is_initialized = False
    
    def __enter__(self):
        """Context manager entry"""
        self.setup_browser()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit"""
        self.close()
    
    def __del__(self):
        """Destructor - ensure browser is closed"""
        if self.page:
            try:
                self.page.quit()
            except:
                pass
