"""
Web Scraper for Magnet Link Conversion
使用DrissionPage进行网页交互和数据捕获
"""

import os
import json
import time
from datetime import datetime
from typing import Dict, List, Optional, Any
from pathlib import Path

from DrissionPage import ChromiumPage, ChromiumOptions
from DrissionPage.errors import ElementNotFoundError, PageDisconnectedError


class WebScraper:
    """
    Web scraper for xiongmaobt.org
    专门用于xiongmaobt.org的网页爬虫，详细记录所有交互过程
    """
    
    def __init__(self, headless: bool = False, capture_dir: str = "captures"):
        """
        Initialize the web scraper
        
        Args:
            headless: Whether to run browser in headless mode
            capture_dir: Directory to save capture data
        """
        self.headless = headless
        self.capture_dir = Path(capture_dir)
        self.capture_dir.mkdir(exist_ok=True)
        
        # Create subdirectories for different types of captures
        (self.capture_dir / "html").mkdir(exist_ok=True)
        (self.capture_dir / "screenshots").mkdir(exist_ok=True)
        (self.capture_dir / "network").mkdir(exist_ok=True)
        (self.capture_dir / "interactions").mkdir(exist_ok=True)
        
        self.page: Optional[ChromiumPage] = None
        self.session_id = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.interaction_log: List[Dict[str, Any]] = []
        
        # Target website
        self.target_url = "https://xiongmaobt.org/"
        
    def setup_browser(self) -> None:
        """Setup browser with optimal settings for scraping"""
        try:
            # Configure browser options
            options = ChromiumOptions()
            
            if self.headless:
                options.headless()
            
            # Add useful options for scraping
            options.add_argument('--no-sandbox')
            options.add_argument('--disable-dev-shm-usage')
            options.add_argument('--disable-blink-features=AutomationControlled')
            options.add_experimental_option("excludeSwitches", ["enable-automation"])
            options.add_experimental_option('useAutomationExtension', False)
            
            # Set user agent to avoid detection
            options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')
            
            # Initialize page
            self.page = ChromiumPage(addr_or_opts=options)
            
            # Execute script to remove webdriver property
            self.page.run_js("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            
            self._log_interaction("browser_setup", {
                "status": "success",
                "headless": self.headless,
                "user_agent": self.page.run_js("return navigator.userAgent"),
                "viewport": self.page.run_js("return {width: window.innerWidth, height: window.innerHeight}")
            })
            
        except Exception as e:
            self._log_interaction("browser_setup", {
                "status": "error",
                "error": str(e)
            })
            raise
    
    def navigate_to_target(self) -> None:
        """Navigate to the target website and capture initial state"""
        if not self.page:
            raise RuntimeError("Browser not initialized. Call setup_browser() first.")
        
        try:
            print(f"Navigating to {self.target_url}...")
            
            # Navigate to the website
            self.page.get(self.target_url)
            
            # Wait for page to load
            time.sleep(3)
            
            # Capture initial state
            self._capture_current_state("initial_load")
            
            self._log_interaction("navigation", {
                "status": "success",
                "url": self.target_url,
                "title": self.page.title,
                "current_url": self.page.url
            })
            
            print(f"Successfully loaded: {self.page.title}")
            print("Page is ready for interaction. You can now interact with the website.")
            print("All interactions will be automatically captured.")
            
        except Exception as e:
            self._log_interaction("navigation", {
                "status": "error",
                "error": str(e),
                "url": self.target_url
            })
            raise
    
    def _capture_current_state(self, action_name: str) -> None:
        """Capture current page state including HTML, screenshot, and network info"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")[:-3]
        
        try:
            # Capture HTML
            html_content = self.page.html
            html_file = self.capture_dir / "html" / f"{self.session_id}_{timestamp}_{action_name}.html"
            with open(html_file, 'w', encoding='utf-8') as f:
                f.write(html_content)
            
            # Capture screenshot
            screenshot_file = self.capture_dir / "screenshots" / f"{self.session_id}_{timestamp}_{action_name}.png"
            self.page.get_screenshot(path=str(screenshot_file))
            
            # Capture page info
            page_info = {
                "timestamp": timestamp,
                "action": action_name,
                "url": self.page.url,
                "title": self.page.title,
                "page_source_length": len(html_content),
                "viewport": self.page.run_js("return {width: window.innerWidth, height: window.innerHeight}"),
                "scroll_position": self.page.run_js("return {x: window.pageXOffset, y: window.pageYOffset}"),
                "cookies": self.page.cookies(),
                "local_storage": self.page.run_js("return JSON.stringify(localStorage)"),
                "session_storage": self.page.run_js("return JSON.stringify(sessionStorage)")
            }
            
            # Save page info
            info_file = self.capture_dir / "interactions" / f"{self.session_id}_{timestamp}_{action_name}_info.json"
            with open(info_file, 'w', encoding='utf-8') as f:
                json.dump(page_info, f, indent=2, ensure_ascii=False)
            
            print(f"Captured state for action: {action_name}")
            
        except Exception as e:
            print(f"Error capturing state for {action_name}: {e}")
    
    def _log_interaction(self, action_type: str, data: Dict[str, Any]) -> None:
        """Log interaction details"""
        log_entry = {
            "timestamp": datetime.now().isoformat(),
            "session_id": self.session_id,
            "action_type": action_type,
            "data": data
        }
        
        self.interaction_log.append(log_entry)
        
        # Save to file
        log_file = self.capture_dir / f"interaction_log_{self.session_id}.json"
        with open(log_file, 'w', encoding='utf-8') as f:
            json.dump(self.interaction_log, f, indent=2, ensure_ascii=False)
    
    def monitor_changes(self, interval: int = 2) -> None:
        """
        Monitor page changes and capture them automatically
        
        Args:
            interval: Monitoring interval in seconds
        """
        if not self.page:
            raise RuntimeError("Browser not initialized.")
        
        print(f"Starting change monitoring (interval: {interval}s)")
        print("Press Ctrl+C to stop monitoring")
        
        last_html = self.page.html
        last_url = self.page.url
        change_counter = 0
        
        try:
            while True:
                time.sleep(interval)
                
                current_html = self.page.html
                current_url = self.page.url
                
                # Check for URL changes
                if current_url != last_url:
                    change_counter += 1
                    print(f"URL changed: {last_url} -> {current_url}")
                    self._capture_current_state(f"url_change_{change_counter}")
                    self._log_interaction("url_change", {
                        "old_url": last_url,
                        "new_url": current_url,
                        "change_number": change_counter
                    })
                    last_url = current_url
                
                # Check for HTML changes (simplified check)
                if len(current_html) != len(last_html) or hash(current_html) != hash(last_html):
                    change_counter += 1
                    print(f"Page content changed (change #{change_counter})")
                    self._capture_current_state(f"content_change_{change_counter}")
                    self._log_interaction("content_change", {
                        "old_length": len(last_html),
                        "new_length": len(current_html),
                        "change_number": change_counter
                    })
                    last_html = current_html
                
        except KeyboardInterrupt:
            print("\nMonitoring stopped by user")
            self._capture_current_state("monitoring_stopped")
    
    def wait_for_interaction(self, message: str = "Waiting for user interaction...") -> None:
        """Wait for user to interact with the page"""
        print(message)
        print("Press Enter when you want to capture the current state, or 'q' to quit")
        
        capture_counter = 0
        
        while True:
            user_input = input().strip().lower()
            
            if user_input == 'q':
                break
            else:
                capture_counter += 1
                self._capture_current_state(f"manual_capture_{capture_counter}")
                print(f"State captured #{capture_counter}. Continue interacting or press 'q' to quit.")
    
    def close(self) -> None:
        """Close the browser and save final logs"""
        if self.page:
            try:
                self._capture_current_state("session_end")
                self._log_interaction("session_end", {
                    "total_interactions": len(self.interaction_log),
                    "final_url": self.page.url,
                    "final_title": self.page.title
                })
                
                self.page.quit()
                print(f"Session ended. All captures saved to: {self.capture_dir}")
                print(f"Total interactions logged: {len(self.interaction_log)}")
                
            except Exception as e:
                print(f"Error during cleanup: {e}")
    
    def __enter__(self):
        """Context manager entry"""
        self.setup_browser()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit"""
        self.close()


def main():
    """Main function for testing the scraper"""
    print("Starting Xiongmaobt.org Web Scraper")
    print("This will open a browser and navigate to the target site")
    print("All interactions will be captured automatically")
    
    try:
        with WebScraper(headless=False) as scraper:
            scraper.navigate_to_target()
            
            # Start monitoring in a separate thread or just wait for manual interaction
            scraper.wait_for_interaction("You can now interact with the website. Press Enter to capture state, 'q' to quit.")
            
    except KeyboardInterrupt:
        print("\nScraper stopped by user")
    except Exception as e:
        print(f"Error: {e}")


if __name__ == "__main__":
    main()
