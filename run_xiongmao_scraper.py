#!/usr/bin/env python3
"""
Xiongmaobt.org Scraper Launcher
xiongmaobt.org爬虫启动器
"""

import sys
import argparse
from pathlib import Path

# Add current directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

try:
    from magnet_xiongmao import XiongmaoBTScraper, Config
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("Please install required dependencies:")
    print("pip install DrissionPage")
    sys.exit(1)


def main():
    """Main launcher function"""
    parser = argparse.ArgumentParser(
        description="XiongmaoBT.org Web Scraper - Automated interaction capture",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python run_xiongmao_scraper.py                     # Interactive mode
  python run_xiongmao_scraper.py --headless          # Headless mode
  python run_xiongmao_scraper.py --monitor           # Auto-monitoring mode
  python run_xiongmao_scraper.py --capture-dir ./my_data  # Custom directory

Modes:
  Interactive: Manual interaction with capture on demand
  Monitor:     Automatic detection and capture of page changes
  Headless:    Run without GUI (useful for servers)

The scraper will capture:
  - Complete HTML snapshots
  - Page screenshots
  - Detailed interaction logs
  - Network and storage data
  - Timestamps and metadata
        """
    )
    
    # Basic options
    parser.add_argument(
        "--headless",
        action="store_true",
        help="Run in headless mode (no browser GUI)"
    )
    
    parser.add_argument(
        "--capture-dir",
        type=str,
        default=None,
        help="Custom directory for captured data"
    )
    
    # Mode selection
    mode_group = parser.add_mutually_exclusive_group()
    mode_group.add_argument(
        "--interactive",
        action="store_true",
        default=True,
        help="Interactive mode (default) - manual interaction"
    )
    
    mode_group.add_argument(
        "--monitor",
        action="store_true",
        help="Auto-monitoring mode - detect page changes"
    )
    
    # Monitoring options
    parser.add_argument(
        "--monitor-interval",
        type=float,
        default=2.0,
        help="Monitoring interval in seconds (default: 2.0)"
    )
    
    parser.add_argument(
        "--monitor-duration",
        type=int,
        default=3600,
        help="Maximum monitoring duration in seconds (default: 3600)"
    )
    
    # Browser options
    parser.add_argument(
        "--window-size",
        type=str,
        default="1920x1080",
        help="Browser window size (default: 1920x1080)"
    )
    
    parser.add_argument(
        "--user-agent",
        type=str,
        help="Custom user agent string"
    )
    
    # Debug options
    parser.add_argument(
        "--debug",
        action="store_true",
        help="Enable debug logging"
    )
    
    args = parser.parse_args()
    
    # Parse window size
    try:
        width, height = map(int, args.window_size.split('x'))
        window_size = (width, height)
    except ValueError:
        print(f"❌ Invalid window size format: {args.window_size}")
        print("Use format: WIDTHxHEIGHT (e.g., 1920x1080)")
        return 1
    
    # Setup debug logging
    if args.debug:
        import logging
        logging.basicConfig(level=logging.DEBUG)
    
    # Create configuration
    config_kwargs = {
        "headless": args.headless,
        "window_size": window_size
    }
    
    if args.capture_dir:
        config_kwargs["capture_dir"] = args.capture_dir
    
    if args.user_agent:
        config_kwargs["user_agent"] = args.user_agent
    
    config = Config(**config_kwargs)
    
    # Display startup info
    print("🚀 XiongmaoBT Scraper Starting")
    print("=" * 50)
    print(f"🎯 Target: {config.TARGET_URL}")
    print(f"📁 Capture Dir: {config.capture_dir}")
    print(f"🖥️  Headless: {'Yes' if args.headless else 'No'}")
    print(f"📐 Window Size: {window_size[0]}x{window_size[1]}")
    
    if args.monitor:
        print(f"🔄 Mode: Auto-monitoring")
        print(f"⏱️  Interval: {args.monitor_interval}s")
        print(f"⏰ Max Duration: {args.monitor_duration}s")
    else:
        print(f"🎮 Mode: Interactive")
    
    print("=" * 50)
    print()
    
    try:
        # Initialize and run scraper
        with XiongmaoBTScraper(config=config) as scraper:
            # Navigate to target
            if not scraper.navigate_to_target():
                print("❌ Failed to navigate to target website")
                return 1
            
            # Run in selected mode
            if args.monitor:
                print("🔄 Starting automatic monitoring...")
                print("Press Ctrl+C to stop and switch to interactive mode")
                try:
                    scraper.start_monitoring(
                        interval=args.monitor_interval,
                        max_duration=args.monitor_duration
                    )
                except KeyboardInterrupt:
                    print("\n⏸️  Monitoring stopped, switching to interactive mode...")
                    scraper.start_interactive_session()
            else:
                print("🎮 Starting interactive session...")
                scraper.start_interactive_session()
        
        # Success summary
        print("\n" + "✅ SESSION COMPLETED" + "\n")
        print("📊 Summary:")
        print(f"   📁 Data saved to: {config.capture_dir}")
        print(f"   📸 Check screenshots: {config.capture_dir}/screenshots/")
        print(f"   📄 Check HTML files: {config.capture_dir}/html/")
        print(f"   📝 Check logs: {config.capture_dir}/session_log_*.json")
        
        print("\n🎯 Next Steps:")
        print("   1. Review captured data for patterns")
        print("   2. Analyze page structure and interactions")
        print("   3. Identify captcha types and handling methods")
        print("   4. Develop automation strategies")
        
        return 0
        
    except KeyboardInterrupt:
        print("\n⚠️  Scraper interrupted by user")
        return 0
    except Exception as e:
        print(f"\n❌ Error occurred: {e}")
        if args.debug:
            import traceback
            traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main())
