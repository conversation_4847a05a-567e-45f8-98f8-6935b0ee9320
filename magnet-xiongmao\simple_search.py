#!/usr/bin/env python3
"""
Simple XiongmaoBT Search Script
简单的熊猫BT搜索脚本 - 直接运行，无复杂菜单
"""

import time
import sys
from datetime import datetime

try:
    from scraper import XiongmaoBTScraper
    from config import Config
except ImportError:
    print("❌ 导入失败，请确保在 magnet-xiongmao 目录下运行")
    sys.exit(1)


def search_xiongmao(keyword, headless=False):
    """
    搜索熊猫BT
    
    Args:
        keyword: 搜索关键词
        headless: 是否无头模式
    """
    print(f"🔍 搜索关键词: {keyword}")
    print(f"🖥️  无头模式: {'是' if headless else '否'}")
    print("=" * 50)
    
    # 创建配置
    config = Config(
        headless=headless,
        capture_dir=f"search_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    )
    
    try:
        with XiongmaoBTScraper(config=config) as scraper:
            print("🌐 正在访问熊猫BT首页...")
            
            # 先访问首页
            if not scraper.navigate_to_target():
                print("❌ 无法访问网站")
                return False
            
            print("✅ 首页加载成功")
            
            # 等待页面完全加载
            time.sleep(3)
            
            # 查找搜索框
            print("🔍 正在查找搜索框...")
            page = scraper.page
            
            # 尝试多种可能的搜索框选择器
            search_selectors = [
                'input[name="keyword"]',
                'input[type="search"]',
                'input[placeholder*="搜索"]',
                'input[placeholder*="关键"]',
                '#search',
                '.search-input',
                'input.form-control'
            ]
            
            search_box = None
            for selector in search_selectors:
                try:
                    search_box = page.ele(selector, timeout=2)
                    if search_box:
                        print(f"✅ 找到搜索框: {selector}")
                        break
                except:
                    continue
            
            if not search_box:
                print("❌ 未找到搜索框，尝试手动查找...")
                # 捕获当前页面状态用于分析
                scraper.capture_current_state("no_search_box_found")
                return False
            
            # 输入搜索关键词
            print(f"⌨️  输入关键词: {keyword}")
            search_box.clear()
            search_box.input(keyword)
            
            # 捕获输入后的状态
            scraper.capture_current_state("after_input")
            
            # 查找搜索按钮
            print("🔍 正在查找搜索按钮...")
            search_button_selectors = [
                'button[type="submit"]',
                'input[type="submit"]',
                'button:contains("搜索")',
                'button:contains("search")',
                '.search-btn',
                '.btn-search'
            ]
            
            search_button = None
            for selector in search_button_selectors:
                try:
                    search_button = page.ele(selector, timeout=2)
                    if search_button:
                        print(f"✅ 找到搜索按钮: {selector}")
                        break
                except:
                    continue
            
            if search_button:
                # 点击搜索按钮
                print("🖱️  点击搜索按钮...")
                search_button.click()
            else:
                # 尝试按回车键
                print("⌨️  尝试按回车键搜索...")
                search_box.input('\n')
            
            # 等待搜索结果加载
            print("⏳ 等待搜索结果...")
            time.sleep(5)
            
            # 捕获搜索结果
            scraper.capture_current_state("search_results")
            
            # 检查当前URL
            current_url = page.url
            print(f"🔗 当前URL: {current_url}")
            
            if "search" in current_url and keyword in current_url:
                print("✅ 搜索成功！")
                
                # 尝试提取搜索结果
                print("📊 正在分析搜索结果...")
                
                # 查找结果列表
                result_selectors = [
                    '.search-result',
                    '.result-item',
                    '.torrent-item',
                    '.list-item',
                    'tr',
                    '.row'
                ]
                
                results_found = False
                for selector in result_selectors:
                    try:
                        results = page.eles(selector)
                        if results and len(results) > 1:  # 排除标题行
                            print(f"✅ 找到 {len(results)} 个结果项")
                            results_found = True
                            break
                    except:
                        continue
                
                if not results_found:
                    print("⚠️  未找到明确的结果列表，但搜索页面已加载")
                
                # 保存最终状态
                scraper.capture_current_state("final_results")
                
                print(f"💾 所有数据已保存到: {config.capture_dir}")
                print("📁 包含:")
                print(f"   - HTML文件: {config.capture_dir}/html/")
                print(f"   - 截图: {config.capture_dir}/screenshots/")
                print(f"   - 交互日志: {config.capture_dir}/interactions/")
                
                return True
                
            else:
                print("❌ 搜索失败，可能被重定向")
                print(f"   期望URL包含: search 和 {keyword}")
                print(f"   实际URL: {current_url}")
                return False
                
    except Exception as e:
        print(f"❌ 发生错误: {e}")
        return False


def main():
    """主函数"""
    print("🚀 熊猫BT简单搜索脚本")
    print("=" * 30)
    
    # 获取搜索关键词
    if len(sys.argv) > 1:
        keyword = sys.argv[1]
    else:
        keyword = input("请输入搜索关键词: ").strip()
    
    if not keyword:
        print("❌ 请提供搜索关键词")
        return 1
    
    # 询问是否无头模式
    headless_input = input("是否使用无头模式? (y/N): ").strip().lower()
    headless = headless_input in ['y', 'yes', '是']
    
    # 执行搜索
    success = search_xiongmao(keyword, headless)
    
    if success:
        print("\n✅ 搜索完成！")
        return 0
    else:
        print("\n❌ 搜索失败！")
        return 1


if __name__ == "__main__":
    sys.exit(main())
