# Magnet Link Converter - Web Scraper Module

## 概述 (Overview)

这个模块专门用于访问 xiongmaobt.org 网站并详细记录所有交互过程，为后续自动化磁力链接获取做准备。

This module is specifically designed to access xiongmaobt.org and capture detailed interaction processes for future automated magnet link extraction.

## 功能特性 (Features)

- 🌐 **自动化浏览器控制**: 使用 DrissionPage 控制 Chrome 浏览器
- 📸 **全面状态捕获**: 自动捕获 HTML、截图、网络信息
- 🔍 **详细交互记录**: 记录每一步操作和页面变化
- 🛡️ **反检测机制**: 配置浏览器以避免被网站检测为机器人
- 📊 **结构化日志**: JSON 格式的详细交互日志
- 🎯 **验证码识别准备**: 为各种验证码场景做好记录准备

## 安装依赖 (Installation)

```bash
pip install -r requirements.txt
```

主要依赖：
- DrissionPage >= 4.0.0
- 其他工具库

## 使用方法 (Usage)

### 基本使用

```python
from magnet_converter import WebScraper

# 创建爬虫实例
scraper = WebScraper(headless=False, capture_dir="my_captures")

# 设置浏览器
scraper.setup_browser()

# 导航到目标网站
scraper.navigate_to_target()

# 等待用户交互
scraper.wait_for_interaction()

# 关闭并保存
scraper.close()
```

### 使用上下文管理器

```python
from magnet_converter import WebScraper

with WebScraper(headless=False) as scraper:
    scraper.navigate_to_target()
    scraper.wait_for_interaction()
```

### 运行示例

```bash
python examples/scrape_xiongmaobt.py
```

## 捕获的数据结构 (Captured Data Structure)

```
captures/
├── html/                    # HTML 快照
│   ├── 20241225_143022_initial_load.html
│   ├── 20241225_143025_content_change_1.html
│   └── ...
├── screenshots/             # 页面截图
│   ├── 20241225_143022_initial_load.png
│   ├── 20241225_143025_content_change_1.png
│   └── ...
├── interactions/           # 详细交互信息
│   ├── 20241225_143022_initial_load_info.json
│   ├── 20241225_143025_content_change_1_info.json
│   └── ...
└── interaction_log_20241225_143022.json  # 完整会话日志
```

## 交互信息包含内容 (Interaction Data Includes)

每次捕获包含以下信息：

- **页面状态**: URL、标题、HTML 内容长度
- **视窗信息**: 浏览器窗口大小、滚动位置
- **存储数据**: Cookies、LocalStorage、SessionStorage
- **时间戳**: 精确到毫秒的时间记录
- **截图**: PNG 格式的页面截图
- **完整 HTML**: 当前页面的完整 HTML 源码

## 配置选项 (Configuration Options)

### WebScraper 参数

- `headless`: 是否无头模式运行 (默认: False)
- `capture_dir`: 捕获数据保存目录 (默认: "captures")

### 浏览器配置

- 反自动化检测
- 自定义 User-Agent
- 禁用开发者工具检测
- 优化的页面加载设置

## 使用场景 (Use Cases)

1. **网站结构分析**: 了解 xiongmaobt.org 的页面结构和交互流程
2. **验证码研究**: 记录各种验证码的出现和处理过程
3. **自动化准备**: 为后续自动化脚本收集必要的交互数据
4. **调试支持**: 详细的日志帮助调试自动化脚本

## 注意事项 (Important Notes)

- 🚨 **合法使用**: 请确保遵守网站的使用条款和相关法律法规
- 🔒 **隐私保护**: 不要记录或分享敏感个人信息
- ⚡ **性能考虑**: 大量捕获可能占用较多磁盘空间
- 🛡️ **反爬虫**: 网站可能有反爬虫机制，请适当调整访问频率

## 下一步计划 (Next Steps)

基于捕获的数据，可以进行：

1. **模式识别**: 分析页面加载和交互模式
2. **自动化脚本开发**: 基于捕获的数据开发自动化脚本
3. **验证码处理**: 开发验证码识别和处理机制
4. **磁力链接提取**: 实现自动化的磁力链接获取功能

## 故障排除 (Troubleshooting)

### 常见问题

1. **浏览器启动失败**: 确保已安装 Chrome 浏览器
2. **权限错误**: 确保有写入捕获目录的权限
3. **网络连接**: 确保能正常访问目标网站

### 调试模式

设置环境变量启用详细日志：

```bash
export DEBUG=1
python examples/scrape_xiongmaobt.py
```
