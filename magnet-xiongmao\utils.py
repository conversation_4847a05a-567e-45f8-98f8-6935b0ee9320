"""
Utility classes for Xiongmaobt.org scraper
xiongmaobt.org爬虫的工具类
"""

import json
import time
import hashlib
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional

try:
    from DrissionPage import ChromiumPage
except ImportError:
    ChromiumPage = None


class SessionLogger:
    """Session logging utility"""
    
    def __init__(self, session_id: str, log_file: Path):
        self.session_id = session_id
        self.log_file = log_file
        self.interactions: List[Dict[str, Any]] = []
    
    def log_interaction(self, action_type: str, data: Dict[str, Any]) -> None:
        """Log an interaction"""
        log_entry = {
            "timestamp": datetime.now().isoformat(),
            "session_id": self.session_id,
            "action_type": action_type,
            "data": data
        }
        
        self.interactions.append(log_entry)
        self._save_to_file()
    
    def _save_to_file(self) -> None:
        """Save interactions to file"""
        try:
            with open(self.log_file, 'w', encoding='utf-8') as f:
                json.dump(self.interactions, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"Error saving log: {e}")
    
    def get_interactions(self) -> List[Dict[str, Any]]:
        """Get all interactions"""
        return self.interactions.copy()
    
    def get_interaction_count(self) -> int:
        """Get total interaction count"""
        return len(self.interactions)


class CaptureManager:
    """Manages capturing of page states"""
    
    def __init__(self, capture_dir: Path, session_id: str):
        self.capture_dir = capture_dir
        self.session_id = session_id
        self.capture_counter = 0
        
        # Ensure subdirectories exist
        (capture_dir / "html").mkdir(exist_ok=True)
        (capture_dir / "screenshots").mkdir(exist_ok=True)
        (capture_dir / "interactions").mkdir(exist_ok=True)
    
    def capture_page_state(self, page: ChromiumPage, action_name: str) -> Dict[str, str]:
        """
        Capture complete page state
        
        Args:
            page: ChromiumPage instance
            action_name: Name of the action being captured
            
        Returns:
            Dictionary with file paths of captured data
        """
        self.capture_counter += 1
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")[:-3]
        
        captured_files = {}
        
        try:
            # Capture HTML
            html_content = page.html
            html_file = self.capture_dir / "html" / f"{self.session_id}_{timestamp}_{action_name}.html"
            with open(html_file, 'w', encoding='utf-8') as f:
                f.write(html_content)
            captured_files["html"] = str(html_file)
            
            # Capture screenshot
            screenshot_file = self.capture_dir / "screenshots" / f"{self.session_id}_{timestamp}_{action_name}.png"
            page.get_screenshot(path=str(screenshot_file))
            captured_files["screenshot"] = str(screenshot_file)
            
            # Capture page information
            page_info = self._extract_page_info(page, action_name, timestamp, html_content)
            info_file = self.capture_dir / "interactions" / f"{self.session_id}_{timestamp}_{action_name}_info.json"
            with open(info_file, 'w', encoding='utf-8') as f:
                json.dump(page_info, f, indent=2, ensure_ascii=False)
            captured_files["info"] = str(info_file)
            
            print(f"✅ Captured state for: {action_name} (#{self.capture_counter})")
            
        except Exception as e:
            print(f"❌ Error capturing state for {action_name}: {e}")
            captured_files["error"] = str(e)
        
        return captured_files
    
    def _extract_page_info(self, page: ChromiumPage, action_name: str, timestamp: str, html_content: str) -> Dict[str, Any]:
        """Extract comprehensive page information"""
        try:
            return {
                "timestamp": timestamp,
                "action": action_name,
                "capture_number": self.capture_counter,
                "url": page.url,
                "title": page.title,
                "page_source_length": len(html_content),
                "page_hash": hashlib.md5(html_content.encode()).hexdigest(),
                "viewport": page.run_js("return {width: window.innerWidth, height: window.innerHeight}"),
                "scroll_position": page.run_js("return {x: window.pageXOffset, y: window.pageYOffset}"),
                "document_ready_state": page.run_js("return document.readyState"),
                "cookies": page.cookies(),
                "local_storage": page.run_js("return JSON.stringify(localStorage)"),
                "session_storage": page.run_js("return JSON.stringify(sessionStorage)"),
                "user_agent": page.run_js("return navigator.userAgent"),
                "current_time": datetime.now().isoformat()
            }
        except Exception as e:
            return {
                "timestamp": timestamp,
                "action": action_name,
                "capture_number": self.capture_counter,
                "error": f"Failed to extract page info: {e}"
            }
    
    def get_capture_count(self) -> int:
        """Get total number of captures"""
        return self.capture_counter


class PageMonitor:
    """Monitors page changes"""
    
    def __init__(self, page: ChromiumPage, capture_manager: CaptureManager, logger: SessionLogger):
        self.page = page
        self.capture_manager = capture_manager
        self.logger = logger
        self.last_url = ""
        self.last_html_hash = ""
        self.change_counter = 0
    
    def start_monitoring(self, interval: float = 2.0, max_duration: int = 3600) -> None:
        """
        Start monitoring page changes
        
        Args:
            interval: Check interval in seconds
            max_duration: Maximum monitoring duration in seconds
        """
        print(f"🔄 Starting page monitoring (interval: {interval}s, max duration: {max_duration}s)")
        print("Press Ctrl+C to stop monitoring")
        
        start_time = time.time()
        self._update_baseline()
        
        try:
            while time.time() - start_time < max_duration:
                time.sleep(interval)
                
                if self._check_for_changes():
                    self.change_counter += 1
                    action_name = f"auto_change_{self.change_counter}"
                    
                    # Capture the change
                    captured_files = self.capture_manager.capture_page_state(self.page, action_name)
                    
                    # Log the change
                    self.logger.log_interaction("page_change", {
                        "change_number": self.change_counter,
                        "url": self.page.url,
                        "captured_files": captured_files,
                        "detection_time": datetime.now().isoformat()
                    })
                    
                    # Update baseline
                    self._update_baseline()
                    
        except KeyboardInterrupt:
            print("\n⏸️  Monitoring stopped by user")
            self.logger.log_interaction("monitoring_stopped", {
                "total_changes_detected": self.change_counter,
                "monitoring_duration": time.time() - start_time
            })
    
    def _check_for_changes(self) -> bool:
        """Check if page has changed"""
        current_url = self.page.url
        current_html = self.page.html
        current_html_hash = hashlib.md5(current_html.encode()).hexdigest()
        
        # Check for URL change
        if current_url != self.last_url:
            print(f"🔗 URL changed: {self.last_url} -> {current_url}")
            return True
        
        # Check for content change
        if current_html_hash != self.last_html_hash:
            print(f"📄 Content changed (hash: {current_html_hash[:8]}...)")
            return True
        
        return False
    
    def _update_baseline(self) -> None:
        """Update baseline values for change detection"""
        self.last_url = self.page.url
        self.last_html_hash = hashlib.md5(self.page.html.encode()).hexdigest()


class InteractionHelper:
    """Helper for user interactions"""
    
    @staticmethod
    def wait_for_user_input(capture_manager: CaptureManager, page: ChromiumPage, logger: SessionLogger) -> None:
        """
        Wait for user input and handle manual captures
        
        Args:
            capture_manager: CaptureManager instance
            page: ChromiumPage instance  
            logger: SessionLogger instance
        """
        print("\n" + "="*50)
        print("🎯 READY FOR INTERACTION")
        print("="*50)
        print("Commands:")
        print("  [Enter]  - Capture current state")
        print("  'q'      - Quit")
        print("  'info'   - Show current page info")
        print("  'help'   - Show this help")
        print("="*50)
        
        manual_capture_count = 0
        
        while True:
            try:
                user_input = input("Command: ").strip().lower()
                
                if user_input == 'q' or user_input == 'quit':
                    break
                elif user_input == 'info':
                    InteractionHelper._show_page_info(page)
                elif user_input == 'help':
                    InteractionHelper._show_help()
                elif user_input == '' or user_input == 'capture':
                    manual_capture_count += 1
                    action_name = f"manual_capture_{manual_capture_count}"
                    captured_files = capture_manager.capture_page_state(page, action_name)
                    
                    logger.log_interaction("manual_capture", {
                        "capture_number": manual_capture_count,
                        "url": page.url,
                        "title": page.title,
                        "captured_files": captured_files
                    })
                    
                    print(f"📸 Manual capture #{manual_capture_count} completed")
                else:
                    print(f"Unknown command: {user_input}. Type 'help' for available commands.")
                    
            except KeyboardInterrupt:
                print("\n👋 Exiting...")
                break
            except EOFError:
                print("\n👋 Exiting...")
                break
    
    @staticmethod
    def _show_page_info(page: ChromiumPage) -> None:
        """Show current page information"""
        try:
            print("\n📊 Current Page Info:")
            print(f"  URL: {page.url}")
            print(f"  Title: {page.title}")
            print(f"  Ready State: {page.run_js('return document.readyState')}")
            viewport = page.run_js("return {width: window.innerWidth, height: window.innerHeight}")
            print(f"  Viewport: {viewport['width']}x{viewport['height']}")
            scroll = page.run_js("return {x: window.pageXOffset, y: window.pageYOffset}")
            print(f"  Scroll: ({scroll['x']}, {scroll['y']})")
            print(f"  Cookies: {len(page.cookies())} items")
            print()
        except Exception as e:
            print(f"❌ Error getting page info: {e}")
    
    @staticmethod
    def _show_help() -> None:
        """Show help information"""
        print("\n📖 Available Commands:")
        print("  [Enter] or 'capture' - Capture current page state")
        print("  'q' or 'quit'        - Exit the scraper")
        print("  'info'               - Show current page information")
        print("  'help'               - Show this help message")
        print("\n💡 Tips:")
        print("  - All interactions are automatically logged")
        print("  - Screenshots and HTML are saved for each capture")
        print("  - Page changes are detected and logged")
        print("  - Check the captures folder for all saved data")
        print()
