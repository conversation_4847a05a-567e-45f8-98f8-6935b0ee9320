#!/usr/bin/env python3
"""
Main entry point for the magnet converter web scraper
磁力链接转换器网页爬虫的主入口点
"""

import sys
import argparse
from pathlib import Path

# Add current directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

from magnet_converter import WebScraper


def main():
    """Main function with command line argument parsing"""
    parser = argparse.ArgumentParser(
        description="Web scraper for xiongmaobt.org - captures detailed interaction data",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python run_scraper.py                    # Run with default settings
  python run_scraper.py --headless         # Run in headless mode
  python run_scraper.py --capture-dir my_captures  # Custom capture directory
  python run_scraper.py --monitor          # Enable automatic change monitoring

The scraper will:
1. Open a browser window (unless --headless is used)
2. Navigate to xiongmaobt.org
3. Capture initial page state
4. Wait for your interaction
5. Capture all changes and interactions
6. Save detailed logs, screenshots, and HTML snapshots

Press Enter to manually capture state, or 'q' to quit.
All data will be saved to the captures directory.
        """
    )
    
    parser.add_argument(
        "--headless",
        action="store_true",
        help="Run browser in headless mode (no GUI)"
    )
    
    parser.add_argument(
        "--capture-dir",
        type=str,
        default="captures",
        help="Directory to save captured data (default: captures)"
    )
    
    parser.add_argument(
        "--monitor",
        action="store_true",
        help="Enable automatic change monitoring"
    )
    
    parser.add_argument(
        "--monitor-interval",
        type=int,
        default=2,
        help="Monitoring interval in seconds (default: 2)"
    )
    
    args = parser.parse_args()
    
    print("🚀 Starting Xiongmaobt.org Web Scraper")
    print("=" * 50)
    print(f"📁 Capture directory: {args.capture_dir}")
    print(f"🖥️  Headless mode: {'Yes' if args.headless else 'No'}")
    print(f"👁️  Auto monitoring: {'Yes' if args.monitor else 'No'}")
    if args.monitor:
        print(f"⏱️  Monitor interval: {args.monitor_interval}s")
    print("=" * 50)
    print()
    
    try:
        # Initialize and run scraper
        with WebScraper(headless=args.headless, capture_dir=args.capture_dir) as scraper:
            print("🌐 Navigating to xiongmaobt.org...")
            scraper.navigate_to_target()
            
            print("\n" + "🎯 READY FOR INTERACTION" + "\n")
            print("Instructions:")
            print("• The browser is now open and ready for interaction")
            print("• All your actions will be automatically captured")
            print("• Press Enter to manually capture current state")
            print("• Type 'q' and press Enter to quit")
            print("• Check the captures folder for all saved data")
            print()
            
            if args.monitor:
                print(f"🔄 Starting automatic change monitoring (interval: {args.monitor_interval}s)")
                print("Press Ctrl+C to stop monitoring and switch to manual mode")
                try:
                    scraper.monitor_changes(interval=args.monitor_interval)
                except KeyboardInterrupt:
                    print("\n⏸️  Automatic monitoring stopped, switching to manual mode")
                    scraper.wait_for_interaction("Manual interaction mode. Press Enter to capture, 'q' to quit:")
            else:
                scraper.wait_for_interaction()
        
        print("\n✅ Session completed successfully!")
        print(f"📊 All captured data saved to: {args.capture_dir}")
        print("\n📁 Captured data includes:")
        print(f"   • HTML snapshots: {args.capture_dir}/html/")
        print(f"   • Screenshots: {args.capture_dir}/screenshots/")
        print(f"   • Interaction logs: {args.capture_dir}/interactions/")
        print(f"   • Session log: {args.capture_dir}/interaction_log_*.json")
        
        print("\n🎯 Next steps:")
        print("1. Review captured data to understand website structure")
        print("2. Identify patterns for automation")
        print("3. Develop captcha handling strategies")
        print("4. Build automated magnet link extraction")
        
    except KeyboardInterrupt:
        print("\n⚠️  Scraper interrupted by user")
    except Exception as e:
        print(f"\n❌ Error occurred: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
