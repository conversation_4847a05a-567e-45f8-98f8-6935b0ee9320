"""
Usage examples for magnet-xiongmao module
magnet-xiongmao模块的使用示例
"""

import sys
from pathlib import Path

# Add parent directory to path
sys.path.insert(0, str(Path(__file__).parent.parent))

from magnet_xiongmao import XiongmaoBTScraper, Config


def example_basic_usage():
    """基本使用示例"""
    print("=== Basic Usage Example ===")
    
    # 使用默认配置
    with XiongmaoBTScraper() as scraper:
        # 导航到网站
        if scraper.navigate_to_target():
            print("✅ Navigation successful")
            
            # 获取页面信息
            info = scraper.get_page_info()
            print(f"📄 Page title: {info.get('title')}")
            print(f"🔗 URL: {info.get('url')}")
            
            # 手动捕获一次状态
            captured = scraper.capture_current_state("example_capture")
            print(f"📸 Captured files: {captured}")
            
            # 开始交互式会话
            scraper.start_interactive_session()


def example_custom_config():
    """自定义配置示例"""
    print("=== Custom Configuration Example ===")
    
    # 创建自定义配置
    config = Config(
        capture_dir="custom_captures",
        headless=False,
        window_size=(1366, 768),
        monitor_interval=3
    )
    
    scraper = XiongmaoBTScraper(config=config)
    
    try:
        scraper.setup_browser()
        
        if scraper.navigate_to_target():
            print("✅ Custom configuration working")
            
            # 显示配置信息
            print(f"📁 Capture directory: {config.capture_dir}")
            print(f"📐 Window size: {config.BROWSER_CONFIG['window_size']}")
            
            # 捕获初始状态
            scraper.capture_current_state("custom_config_test")
            
            print("🎮 Ready for interaction...")
            input("Press Enter to continue...")
            
    finally:
        scraper.close()


def example_monitoring_mode():
    """自动监控模式示例"""
    print("=== Auto-Monitoring Mode Example ===")
    
    config = Config(
        capture_dir="monitoring_captures",
        headless=False
    )
    
    with XiongmaoBTScraper(config=config) as scraper:
        if scraper.navigate_to_target():
            print("🔄 Starting monitoring mode...")
            print("The scraper will automatically detect and capture page changes")
            print("Interact with the website in the browser window")
            print("Press Ctrl+C to stop monitoring")
            
            try:
                # 监控5分钟，每2秒检查一次
                scraper.start_monitoring(interval=2, max_duration=300)
            except KeyboardInterrupt:
                print("\n⏸️  Monitoring stopped by user")


def example_headless_mode():
    """无头模式示例"""
    print("=== Headless Mode Example ===")
    
    config = Config(
        capture_dir="headless_captures",
        headless=True
    )
    
    with XiongmaoBTScraper(config=config) as scraper:
        if scraper.navigate_to_target():
            print("👻 Running in headless mode")
            
            # 在无头模式下，我们只能进行程序化操作
            info = scraper.get_page_info()
            print(f"📊 Page info: {info}")
            
            # 捕获几个状态
            for i in range(3):
                scraper.capture_current_state(f"headless_capture_{i+1}")
                print(f"📸 Captured state {i+1}")
                
                # 模拟一些等待时间
                import time
                time.sleep(2)


def example_programmatic_interaction():
    """程序化交互示例"""
    print("=== Programmatic Interaction Example ===")
    
    with XiongmaoBTScraper(headless=False) as scraper:
        if scraper.navigate_to_target():
            print("🤖 Demonstrating programmatic interactions")
            
            # 获取页面对象进行直接操作
            page = scraper.page
            
            # 示例：查找和点击元素（需要根据实际页面调整）
            try:
                # 等待页面加载
                import time
                time.sleep(3)
                
                # 捕获初始状态
                scraper.capture_current_state("before_interaction")
                
                # 这里可以添加具体的页面交互逻辑
                # 例如：点击按钮、填写表单等
                # button = page.ele('xpath://button[@class="search-btn"]')
                # if button:
                #     button.click()
                #     scraper.capture_current_state("after_button_click")
                
                print("💡 Add your specific interaction logic here")
                print("🔍 Use page.ele() to find elements")
                print("🖱️  Use element.click() to click")
                print("⌨️  Use element.input() to type")
                
                # 最终捕获
                scraper.capture_current_state("final_state")
                
            except Exception as e:
                print(f"⚠️  Interaction error: {e}")
                print("💡 This is normal if the page structure is different")


def main():
    """主函数 - 运行所有示例"""
    examples = [
        ("Basic Usage", example_basic_usage),
        ("Custom Configuration", example_custom_config),
        ("Monitoring Mode", example_monitoring_mode),
        ("Headless Mode", example_headless_mode),
        ("Programmatic Interaction", example_programmatic_interaction)
    ]
    
    print("🎯 XiongmaoBT Scraper Examples")
    print("=" * 50)
    
    for i, (name, func) in enumerate(examples, 1):
        print(f"{i}. {name}")
    
    print("0. Exit")
    print("=" * 50)
    
    while True:
        try:
            choice = input("\nSelect example (0-5): ").strip()
            
            if choice == "0":
                print("👋 Goodbye!")
                break
            
            try:
                choice_idx = int(choice) - 1
                if 0 <= choice_idx < len(examples):
                    name, func = examples[choice_idx]
                    print(f"\n🚀 Running: {name}")
                    print("-" * 30)
                    func()
                    print("-" * 30)
                    print(f"✅ {name} completed")
                else:
                    print("❌ Invalid choice. Please select 0-5.")
            except ValueError:
                print("❌ Please enter a number.")
                
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"❌ Error: {e}")


if __name__ == "__main__":
    main()
