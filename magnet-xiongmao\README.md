# Magnet-Xiongmao Module

专门用于 xiongmaobt.org 网站的磁力链接获取模块

## 概述

这是一个独立的 Python 模块，专门设计用于与 xiongmaobt.org 网站进行交互，并详细记录所有操作过程。该模块为后续自动化磁力链接获取提供基础数据收集功能。

## 特性

- 🎯 **专门针对 xiongmaobt.org** - 优化的配置和反检测机制
- 🤖 **智能浏览器控制** - 基于 DrissionPage 的自动化控制
- 📊 **全面数据捕获** - HTML、截图、交互日志、网络信息
- 🛡️ **反检测机制** - 多层反爬虫检测规避
- 📝 **详细日志记录** - 结构化的交互和状态日志
- 🔄 **自动监控** - 实时检测页面变化
- 🎮 **交互式操作** - 支持手动交互和自动化

## 安装

```bash
pip install DrissionPage
```

## 快速开始

### 方式一：直接运行模块

```bash
# 进入模块目录
cd magnet-xiongmao

# 启动器 (选择运行方式)
python launcher.py

# 简单运行
python run.py

# 主程序 (支持命令行参数)
python main.py

# 作为模块运行
python -m magnet_xiongmao.main
```

### 方式二：作为模块导入

```python
from magnet_xiongmao import XiongmaoBTScraper, quick_start

# 快速开始
scraper = quick_start()
scraper.setup_browser()
scraper.navigate_to_target()
scraper.start_interactive_session()
scraper.close()

# 或使用上下文管理器
with XiongmaoBTScraper() as scraper:
    scraper.navigate_to_target()
    scraper.start_interactive_session()
```

### 自定义配置

```python
from magnet_xiongmao import XiongmaoBTScraper, Config

# 自定义配置
config = Config(
    capture_dir="my_captures",
    headless=True
)

scraper = XiongmaoBTScraper(config=config)
scraper.setup_browser()
scraper.navigate_to_target()

# 手动捕获状态
scraper.capture_current_state("custom_action")

# 获取页面信息
info = scraper.get_page_info()
print(info)

scraper.close()
```

### 自动监控模式

```python
from magnet_xiongmao import XiongmaoBTScraper

with XiongmaoBTScraper(headless=False) as scraper:
    scraper.navigate_to_target()

    # 启动自动监控 (每2秒检查一次变化)
    scraper.start_monitoring(interval=2, max_duration=1800)
```

## 配置选项

### 基本配置

```python
config = Config(
    capture_dir="captures",           # 捕获数据保存目录
    headless=False,                   # 是否无头模式
    window_size=(1920, 1080),        # 浏览器窗口大小
    page_load_timeout=30,             # 页面加载超时
    monitor_interval=2                # 监控间隔(秒)
)
```

### 环境变量配置

```bash
export XIONGMAO_HEADLESS=true
export XIONGMAO_CAPTURE_DIR=/path/to/captures
export XIONGMAO_MONITOR_INTERVAL=5
export XIONGMAO_USER_AGENT="Custom User Agent"
```

## API 参考

### XiongmaoBTScraper

主要的爬虫类，提供完整的网站交互功能。

#### 方法

- `setup_browser()` - 初始化浏览器
- `navigate_to_target()` - 导航到目标网站
- `start_interactive_session()` - 开始交互式会话
- `start_monitoring(interval, max_duration)` - 开始自动监控
- `capture_current_state(action_name)` - 手动捕获当前状态
- `get_page_info()` - 获取当前页面信息
- `close()` - 关闭浏览器并保存数据

### Config

配置管理类，处理所有配置选项。

#### 属性

- `TARGET_URL` - 目标网站 URL
- `BROWSER_CONFIG` - 浏览器配置
- `ANTI_DETECTION` - 反检测设置
- `CAPTURE_CONFIG` - 捕获配置
- `MONITOR_CONFIG` - 监控配置

### 工具类

- `CaptureManager` - 管理页面状态捕获
- `SessionLogger` - 会话日志记录
- `PageMonitor` - 页面变化监控
- `InteractionHelper` - 用户交互辅助

## 数据结构

### 捕获目录结构

```
captures/xiongmaobt/
├── html/                           # HTML快照
│   ├── 20241225_143022_initial_load.html
│   └── 20241225_143025_manual_capture_1.html
├── screenshots/                    # 页面截图
│   ├── 20241225_143022_initial_load.png
│   └── 20241225_143025_manual_capture_1.png
├── interactions/                   # 详细交互信息
│   ├── 20241225_143022_initial_load_info.json
│   └── 20241225_143025_manual_capture_1_info.json
├── logs/                          # 日志文件
└── session_log_20241225_143022.json  # 完整会话日志
```

### 交互日志格式

```json
{
  "timestamp": "2024-12-25T14:30:22.123456",
  "session_id": "20241225_143022",
  "action_type": "navigation",
  "data": {
    "status": "success",
    "target_url": "https://xiongmaobt.org/",
    "final_url": "https://xiongmaobt.org/",
    "title": "熊猫BT",
    "captured_files": {
      "html": "/path/to/html/file.html",
      "screenshot": "/path/to/screenshot.png",
      "info": "/path/to/info.json"
    }
  }
}
```

## 使用场景

1. **网站结构分析** - 了解页面布局和交互流程
2. **验证码研究** - 记录和分析各种验证码类型
3. **自动化开发** - 为自动化脚本收集必要数据
4. **调试支持** - 详细日志帮助问题诊断
5. **行为模式学习** - 分析用户交互模式

## 注意事项

- ⚖️ **合法使用** - 请遵守网站使用条款和相关法律
- 🔒 **隐私保护** - 不要记录敏感个人信息
- 💾 **存储空间** - 大量捕获会占用磁盘空间
- 🚦 **访问频率** - 适当控制访问频率避免被封

## 故障排除

### 常见问题

1. **导入错误** - 确保已安装 DrissionPage: `pip install DrissionPage`
2. **浏览器启动失败** - 确保系统已安装 Chrome 浏览器
3. **权限错误** - 确保对捕获目录有写入权限
4. **网络连接** - 确保能正常访问目标网站

### 调试模式

```python
import logging
logging.basicConfig(level=logging.DEBUG)

# 然后运行你的代码
```

## 开发计划

- [ ] 验证码自动识别
- [ ] 磁力链接自动提取
- [ ] 代理支持
- [ ] 分布式爬取
- [ ] 数据分析工具

## 许可证

本模块仅供学习和研究使用，请遵守相关法律法规。
